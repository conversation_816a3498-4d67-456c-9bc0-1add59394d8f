<?php
declare(strict_types=1);

namespace app\index\controller;

use app\common\controller\CommonController;
use app\common\service\WechatWorkService;
use app\admin\model\SystemOrder;
use think\facade\Log;
use think\facade\Request;
use callback\WXBizMsgCrypt;

/**
 * 企业微信客服控制器
 */
class WechatWork extends CommonController
{
    private $wechatWorkService;

    public function __construct()
    {
        $this->wechatWorkService = new WechatWorkService();
    }

    /**
     * 企业微信消息接收入口
     */
    public function index()
    {
        $method = Request::method();
        
        if ($method === 'GET') {
            // 验证URL有效性
            return $this->verifyUrl();
        } elseif ($method === 'POST') {
            // 处理消息
            return $this->handleMessage();
        }
        
        return 'Method not allowed';
    }

    /**
     * 验证URL有效性（企业微信回调验证）
     */
    private function verifyUrl()
    {
        $signature = Request::get('msg_signature');
        $timestamp = Request::get('timestamp');
        $nonce = Request::get('nonce');
        $echostr = Request::get('echostr');

        // 获取企业微信配置
        $token = get_setting('wechat_work_token');
        $encodingAESKey = get_setting('wechat_work_encoding_aes_key');
        $corpId = get_setting('wechat_work_corp_id');

        // 检查配置是否完整
        if (empty($token) || empty($encodingAESKey) || empty($corpId)) {
            Log::error('企业微信配置不完整: token=' . ($token ? '已设置' : '未设置') .
                      ', encodingAESKey=' . ($encodingAESKey ? '已设置' : '未设置') .
                      ', corpId=' . ($corpId ? '已设置' : '未设置'));
            return 'fail';
        }

        try {
            // 尝试引入企业微信加密库
            $cryptFile = root_path() . 'extend/callback/WXBizMsgCrypt.php';
            if (file_exists($cryptFile)) {
                require_once $cryptFile;
                
                // 使用企业微信官方加密库进行验证
                if (class_exists('callback\\WXBizMsgCrypt')) {
                    $wxcpt = new WXBizMsgCrypt($token, $encodingAESKey, $corpId);
                    $sEchoStr = "";
                    $errCode = $wxcpt->VerifyURL($signature, $timestamp, $nonce, $echostr, $sEchoStr);
                    
                    if ($errCode == 0) {
                        Log::info('企业微信URL验证成功');
                        return $sEchoStr;
                    } else {
                        Log::error('企业微信URL验证失败，错误码: ' . $errCode);
                        return 'fail';
                    }
                }
            }
            
            Log::warning('企业微信加密库不存在或无法加载，使用简化验证模式: ' . $cryptFile);
            
            // 如果没有加密库，使用简化验证（仅用于测试环境）
            $tmpArr = array($token, $timestamp, $nonce);
            sort($tmpArr, SORT_STRING);
            $tmpStr = implode($tmpArr);
            $tmpStr = sha1($tmpStr);
            
            if ($tmpStr == $signature) {
                Log::info('企业微信URL验证成功（简化模式）');
                return $echostr;
            } else {
                Log::error('企业微信URL签名验证失败');
                Log::error('期望签名: ' . $tmpStr);
                Log::error('实际签名: ' . $signature);
                return 'fail';
            }
        } catch (\Exception $e) {
            Log::error('企业微信URL验证异常: ' . $e->getMessage());
            return 'fail';
        }
    }

    /**
     * 处理企业微信消息
     */
    private function handleMessage()
    {
        try {
            $input = file_get_contents('php://input');
            Log::info('企业微信消息接收: ' . $input);

            // 检查输入是否为空
            if (empty($input)) {
                Log::error('企业微信消息内容为空');
                return 'fail';
            }

            // 获取参数
            $msgSignature = Request::get('msg_signature');
            $timestamp = Request::get('timestamp');
            $nonce = Request::get('nonce');

            // 记录参数用于调试
            Log::info('企业微信消息参数: msg_signature=' . $msgSignature . ', timestamp=' . $timestamp . ', nonce=' . $nonce);

            // 检查是否是加密消息
            $isEncrypted = !empty($msgSignature) && !empty($timestamp) && !empty($nonce);

            if ($isEncrypted) {
                Log::info('处理加密企业微信消息');
                // 处理加密消息
                $response = $this->wechatWorkService->handleEncryptedMessage($msgSignature, $timestamp, $nonce, $input);
                
                Log::info('加密消息处理结果: ' . json_encode($response));
                
                // 返回响应
                if ($response && isset($response['type']) && $response['type'] === 'text' && !empty($response['content'])) {
                    // 对于加密消息，需要返回加密后的响应
                    return $this->buildEncryptedResponse($response['content'], $timestamp, $nonce);
                } else {
                    Log::warning('加密消息处理结果无效: ' . json_encode($response));
                }
            } else {
                Log::info('处理明文企业微信消息');
                // 解析XML消息
                $message = $this->parseXmlMessage($input);
                if (!$message) {
                    Log::error('企业微信XML消息解析失败');
                    return 'fail';
                }

                Log::info('企业微信消息解析成功: ' . json_encode($message));

                // 处理消息
                $response = $this->wechatWorkService->handleMessage($message);
                
                Log::info('消息处理结果: ' . json_encode($response));
                
                // 返回响应
                if ($response && isset($response['type']) && $response['type'] === 'text' && !empty($response['content'])) {
                    return $this->buildTextResponse($message, $response['content']);
                } else {
                    Log::warning('消息处理结果无效: ' . json_encode($response));
                }
            }

            return 'success';

        } catch (\Exception $e) {
            Log::error('处理企业微信消息失败: ' . $e->getMessage() . '，堆栈: ' . $e->getTraceAsString());
            return 'fail';
        }
    }

    /**
     * 解析XML消息
     */
    private function parseXmlMessage(string $xml): array
    {
        try {
            // 检查XML内容是否为空
            if (empty(trim($xml))) {
                Log::error('企业微信XML消息内容为空');
                return [];
            }
            
            // 禁用XML错误报告，避免抛出异常
            libxml_use_internal_errors(true);
            
            $data = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
            
            // 清除XML错误
            libxml_clear_errors();
            
            if (!$data) {
                Log::error('企业微信XML消息解析失败: simplexml_load_string返回false');
                return [];
            }

            // 解析所有可能的字段，包括客服事件字段
            $message = [
                'ToUserName' => (string)$data->ToUserName,
                'FromUserName' => (string)$data->FromUserName,
                'CreateTime' => (string)$data->CreateTime,
                'MsgType' => (string)$data->MsgType,
                'Content' => (string)$data->Content,
                'MsgId' => (string)$data->MsgId,
                'AgentID' => (string)$data->AgentID,
                'Event' => (string)$data->Event,
                'Token' => (string)$data->Token,
                'OpenKfId' => (string)$data->OpenKfId,
                'ChangeType' => (string)$data->ChangeType,
                'UserId' => (string)$data->UserId,
                'ExternalUserId' => (string)$data->ExternalUserId,
                'State' => (string)$data->State,
            ];

            // 记录完整的XML结构用于调试
            Log::info('企业微信XML原始结构: ' . $xml);
            Log::info('企业微信XML消息解析成功: ' . json_encode($message));
            
            return $message;

        } catch (\Exception $e) {
            Log::error('解析企业微信XML消息失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 构建文本回复消息
     */
    private function buildTextResponse(array $message, string $content): string
    {
        $time = time();
        $fromUser = $message['ToUserName'];
        $toUser = $message['FromUserName'];

        $xml = "<xml>
            <ToUserName><![CDATA[{$toUser}]]></ToUserName>
            <FromUserName><![CDATA[{$fromUser}]]></FromUserName>
            <CreateTime>{$time}</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[{$content}]]></Content>
        </xml>";

        return $xml;
    }

    /**
     * 构建加密回复消息
     */
    private function buildEncryptedResponse(string $content, string $timestamp, string $nonce): string
    {
        try {
            // 获取企业微信配置
            $token = get_setting('wechat_work_token');
            $encodingAESKey = get_setting('wechat_work_encoding_aes_key');
            $corpId = get_setting('wechat_work_corp_id');

            // 检查配置是否完整
            if (empty($token) || empty($encodingAESKey) || empty($corpId)) {
                Log::error('企业微信配置不完整，无法构建加密响应');
                return 'fail';
            }

            // 尝试引入企业微信加密库
            $cryptFile = root_path() . 'extend/callback/WXBizMsgCrypt.php';
            if (file_exists($cryptFile)) {
                require_once $cryptFile;
                
                if (class_exists('callback\\WXBizMsgCrypt')) {
                    // 构建XML消息
                    $xmlMsg = $this->buildTextXml($content);
                    
                    // 加密消息
                    $wxcpt = new WXBizMsgCrypt($token, $encodingAESKey, $corpId);
                    $sEncryptMsg = '';
                    $errCode = $wxcpt->EncryptMsg($xmlMsg, $timestamp, $nonce, $sEncryptMsg);
                    
                    if ($errCode == 0) {
                        return $sEncryptMsg;
                    } else {
                        Log::error('企业微信消息加密失败，错误码: ' . $errCode);
                        return 'fail';
                    }
                }
            }
            
            Log::warning('企业微信加密库不存在，无法构建加密响应');
            return 'fail';
            
        } catch (\Exception $e) {
            Log::error('构建企业微信加密响应异常: ' . $e->getMessage());
            return 'fail';
        }
    }

    /**
     * 构建文本XML消息
     */
    private function buildTextXml(string $content): string
    {
        $time = time();
        return "<xml>
            <Content><![CDATA[{$content}]]></Content>
            <CreateTime>{$time}</CreateTime>
            <MsgType><![CDATA[text]]></MsgType>
        </xml>";
    }

    /**
     * 支付通知回调
     */
    public function notify()
    {
        try {
            $tradeNo = Request::param('trade_no');
            $status = Request::param('status', 0);

            if (!$tradeNo) {
                return json(['code' => 0, 'msg' => '订单号不能为空']);
            }

            // 更新订单状态
            $order = SystemOrder::where('trade_no', $tradeNo)->find();
            if (!$order) {
                return json(['code' => 0, 'msg' => '订单不存在']);
            }

            if ($status == 1) {
                $order->status = 1;
                $order->pay_time = time();
                $order->save();

                // 发送支付成功通知
                if (!empty($order['wechat_work_user'])) {
                    $successMessage = "✅ 支付成功！\n\n" .
                                    "💰 支付金额：¥{$order['money']}\n" .
                                    "📋 订单号：{$tradeNo}\n" .
                                    "⏰ 支付时间：" . date('Y-m-d H:i:s') . "\n\n" .
                                    "感谢您的支付！";
                    
                    $this->wechatWorkService->sendTextMessage($order['wechat_work_user'], $successMessage);
                }
            }

            return json(['code' => 1, 'msg' => '处理成功']);

        } catch (\Exception $e) {
            Log::error('企业微信支付通知处理失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '处理失败']);
        }
    }

    /**
     * 支付成功返回页面
     */
    public function returnPage()
    {
        $tradeNo = Request::param('trade_no');
        
        if (!$tradeNo) {
            $this->error('订单号不能为空');
        }

        $order = SystemOrder::where('trade_no', $tradeNo)->find();
        if (!$order) {
            $this->error('订单不存在');
        }

        $data = [
            'order' => $order,
            'title' => '支付成功',
            'message' => '您的支付已成功完成，感谢您的使用！'
        ];

        return view('wechatwork/success', $data);
    }

    /**
     * 手动发送支付二维码（测试用）
     */
    public function sendQRCode()
    {
        $userId = Request::param('user_id');
        $tradeNo = Request::param('trade_no');
        $amount = Request::param('amount', 0);

        if (!$userId || !$tradeNo || $amount <= 0) {
            return json(['code' => 0, 'msg' => '参数不完整']);
        }

        $result = $this->wechatWorkService->sendPaymentQRCode($userId, $tradeNo, $amount);

        return json([
            'code' => $result ? 1 : 0,
            'msg' => $result ? '发送成功' : '发送失败'
        ]);
    }

    /**
     * 生成企业微信客服链接并自动发送支付消息
     */
    public function generateCustomerServiceLink()
    {
        try {
            $tradeNo = Request::param('trade_no');
            $amount = Request::param('amount');

            if (!$tradeNo || !$amount) {
                return json(['code' => 0, 'msg' => '参数不完整']);
            }

            // 获取企业微信配置
            $corpId = get_setting('wechat_work_corp_id');
            $agentId = get_setting('wechat_work_agent_id');
            $enabled = get_setting('wechat_work_enabled');

            if (!$enabled || !$corpId || !$agentId) {
                return json(['code' => 0, 'msg' => '企业微信客服未启用或配置不完整']);
            }

            // 获取订单信息
            $orderInfo = [
                'name' => Request::param('product_name', '商品支付'),
                'trade_no' => $tradeNo,
                'amount' => $amount
            ];

            // 生成客服链接和自动发送消息
            $result = $this->wechatWorkService->generateCustomerServiceLink($tradeNo, floatval($amount), $orderInfo);

            if ($result['success']) {
                return json([
                    'code' => 1,
                    'msg' => $result['message'],
                    'data' => [
                        'customer_service_url' => $result['customer_service_url'],
                        'session_id' => $result['session_id']
                    ]
                ]);
            } else {
                return json(['code' => 0, 'msg' => $result['message']]);
            }

        } catch (\Exception $e) {
            Log::error('生成企业微信客服链接失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '生成客服链接失败']);
        }
    }

    /**
     * 处理客服会话访问（用于自动发送消息）
     */
    public function handleServiceAccess()
    {
        try {
            $sessionId = Request::param('session_id');

            if (!$sessionId) {
                return json(['code' => 0, 'msg' => '会话ID不能为空']);
            }

            // 获取缓存中的客服数据
            $cacheKey = 'wechat_service_' . $sessionId;
            $serviceData = cache($cacheKey);

            if (!$serviceData) {
                return json(['code' => 0, 'msg' => '客服会话数据不存在']);
            }

            // 获取企业微信配置
            $corpId = get_setting('wechat_work_corp_id');
            $agentId = get_setting('wechat_work_agent_id');

            if (!$corpId || !$agentId) {
                return json(['code' => 0, 'msg' => '企业微信配置不完整']);
            }

            // 使用企业微信API生成客服链接
            $wechatWorkService = new \app\common\service\WechatWorkService();
            $customerServiceUrl = $wechatWorkService->buildCustomerServiceUrl($sessionId, $serviceData['temp_user_id']);

            if (empty($customerServiceUrl)) {
                return json(['code' => 0, 'msg' => '生成客服链接失败']);
            }

            // 更新缓存，添加客服链接
            $serviceData['customer_service_url'] = $customerServiceUrl;
            cache($cacheKey, $serviceData, 1800);

            // 返回客服链接和消息
            return json([
                'code' => 1,
                'msg' => '客服链接获取成功',
                'data' => [
                    'customer_service_url' => $customerServiceUrl,
                    'session_id' => $sessionId,
                    'message' => $serviceData['message'],
                    'media_id' => $serviceData['media_id']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('处理客服会话访问失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '处理失败']);
        }
    }

    /**
     * 处理企业微信OAuth2回调
     */
    public function oauthCallback()
    {
        try {
            $code = Request::param('code');
            $state = Request::param('state');
            $appId = Request::param('appid');

            if (!$code || !$state) {
                return '参数错误';
            }

            // 获取企业微信配置
            $corpId = get_setting('wechat_work_corp_id');
            $corpSecret = get_setting('wechat_work_corp_secret');

            if (!$corpId || !$corpSecret) {
                return '企业微信配置不完整';
            }

            // 使用code获取用户信息
            $accessTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={$corpId}&corpsecret={$corpSecret}";
            $tokenResponse = file_get_contents($accessTokenUrl);
            $tokenData = json_decode($tokenResponse, true);

            if (!isset($tokenData['access_token'])) {
                Log::error('获取企业微信access_token失败: ' . $tokenResponse);
                return '获取访问令牌失败';
            }

            $accessToken = $tokenData['access_token'];

            // 获取用户信息
            $userInfoUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={$accessToken}&code={$code}";
            $userInfoResponse = file_get_contents($userInfoUrl);
            $userInfo = json_decode($userInfoResponse, true);

            if (!isset($userInfo['UserId'])) {
                Log::error('获取企业微信用户信息失败: ' . $userInfoResponse);
                return '获取用户信息失败';
            }

            $userId = $userInfo['UserId'];

            // 处理客服会话访问，使用真实用户ID发送支付消息
            try {
                $result = $this->wechatWorkService->handleCustomerServiceAccessWithUser($state, $userId);

                if ($result) {
                    // 返回成功页面
                    return $this->showSuccessPage('支付消息已发送', '企业微信客服支付消息已成功发送，请在企业微信中查看。');
                } else {
                    Log::error('企业微信客服会话访问处理失败: handleCustomerServiceAccessWithUser返回false');
                    return $this->showErrorPage('发送失败', '支付消息发送失败，请重试或联系客服。');
                }
            } catch (\Exception $e) {
                Log::error('企业微信客服会话访问处理异常: ' . $e->getMessage());
                return $this->showErrorPage('发送失败', '支付消息发送异常：' . $e->getMessage());
            }

        } catch (\Exception $e) {
            Log::error('处理企业微信OAuth2回调失败: ' . $e->getMessage());
            return '处理失败';
        }
    }

    /**
     * 显示成功页面
     */
    private function showSuccessPage($title, $message)
    {
        return "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <title>{$title}</title>
    <style>
        body { font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; text-align: center; padding: 50px 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .icon { font-size: 64px; color: #07c160; margin-bottom: 20px; }
        h1 { color: #333; margin-bottom: 20px; }
        p { color: #666; line-height: 1.6; margin-bottom: 30px; }
        .btn { display: inline-block; background: #07c160; color: white; padding: 12px 24px; border-radius: 4px; text-decoration: none; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='icon'>✓</div>
        <h1>{$title}</h1>
        <p>{$message}</p>
        <a href='javascript:window.close();' class='btn'>关闭页面</a>
    </div>
</body>
</html>";
    }

    /**
     * 显示错误页面
     */
    private function showErrorPage($title, $message)
    {
        return "<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <title>{$title}</title>
    <style>
        body { font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; text-align: center; padding: 50px 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .icon { font-size: 64px; color: #fa5151; margin-bottom: 20px; }
        h1 { color: #333; margin-bottom: 20px; }
        p { color: #666; line-height: 1.6; margin-bottom: 30px; }
        .btn { display: inline-block; background: #07c160; color: white; padding: 12px 24px; border-radius: 4px; text-decoration: none; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='icon'>✗</div>
        <h1>{$title}</h1>
        <p>{$message}</p>
        <a href='javascript:window.close();' class='btn'>关闭页面</a>
    </div>
</body>
</html>";
    }

    /**
     * 测试企业微信连接
     */
    public function testConnection()
    {
        try {
            // 获取测试参数
            $corpId = Request::param('corp_id');
            $corpSecret = Request::param('corp_secret');
            $agentId = Request::param('agent_id');
            $token = Request::param('token');
            $encodingAESKey = Request::param('encoding_aes_key');

            if (!$corpId || !$corpSecret || !$agentId || !$token || !$encodingAESKey) {
                return json(['code' => 0, 'msg' => '配置参数不完整']);
            }

            // 临时设置配置进行测试
            $originalCorpId = get_setting('wechat_work_corp_id');
            $originalCorpSecret = get_setting('wechat_work_corp_secret');
            $originalAgentId = get_setting('wechat_work_agent_id');
            $originalToken = get_setting('wechat_work_token');
            $originalEncodingAESKey = get_setting('wechat_work_encoding_aes_key');

            // 临时更新配置
            set_setting('wechat_work_corp_id', $corpId);
            set_setting('wechat_work_corp_secret', $corpSecret);
            set_setting('wechat_work_agent_id', $agentId);
            set_setting('wechat_work_token', $token);
            set_setting('wechat_work_encoding_aes_key', $encodingAESKey);

            // 创建新的服务实例进行测试
            $testService = new \app\common\service\WechatWorkService();
            $accessToken = $testService->getAccessToken();

            // 恢复原配置
            set_setting('wechat_work_corp_id', $originalCorpId);
            set_setting('wechat_work_corp_secret', $originalCorpSecret);
            set_setting('wechat_work_agent_id', $originalAgentId);
            set_setting('wechat_work_token', $originalToken);
            set_setting('wechat_work_encoding_aes_key', $originalEncodingAESKey);

            if ($accessToken) {
                return json(['code' => 1, 'msg' => '连接成功', 'token' => substr($accessToken, 0, 10) . '...']);
            } else {
                return json(['code' => 0, 'msg' => '连接失败，请检查配置']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '连接异常: ' . $e->getMessage()]);
        }
    }

    /**
     * 企业微信客服演示页面
     */
    public function demo()
    {
        $data = [
            'title' => '企业微信客服支付演示',
            'corp_id' => get_setting('wechat_work_corp_id'),
            'enabled' => get_setting('wechat_work_enabled', 0)
        ];

        return view('../wechatwork/demo', $data);
    }

    /**
     * 企业微信诊断页面
     */
    public function diagnosePage()
    {
        return view('../wechatwork/diagnose');
    }

    /**
     * 企业微信测试页面
     */
    public function testPage()
    {
        return view('../wechatwork/test');
    }

    /**
     * 测试二维码生成
     */
    public function testQRCodeGeneration()
    {
        try {
            // 创建测试订单
            $testTradeNo = 'TEST_' . date('YmdHis') . rand(1000, 9999);
            $testAmount = 0.01; // 测试金额1分钱

            $wechatWorkService = new \app\common\service\WechatWorkService();

            // 测试二维码生成
            $reflection = new \ReflectionClass($wechatWorkService);
            $method = $reflection->getMethod('generatePaymentQRCode');
            $method->setAccessible(true);

            // 创建测试订单数据
            $testOrder = new \app\admin\model\SystemOrder();
            $testOrder->save([
                'trade_no' => $testTradeNo,
                'out_trade_no' => $testTradeNo,
                'pid' => 1,
                'type' => 1,
                'name' => '企业微信测试订单',
                'money' => $testAmount,
                'truemoney' => $testAmount,
                'notify_url' => request()->domain() . '/wechatwork/notify',
                'return_url' => request()->domain() . '/wechatwork/return',
                'status' => 0,
                'create_time' => time()
            ]);

            $qrCodePath = $method->invoke($wechatWorkService, $testTradeNo, $testAmount);

            if ($qrCodePath && file_exists($qrCodePath)) {
                $fileSize = filesize($qrCodePath);
                // 清理测试文件
                unlink($qrCodePath);
                // 清理测试订单
                $testOrder->where('trade_no', $testTradeNo)->delete();

                return json([
                    'code' => 1,
                    'msg' => '二维码生成测试通过',
                    'data' => [
                        'file_size' => $fileSize,
                        'test_order' => $testTradeNo
                    ]
                ]);
            } else {
                // 清理测试订单
                $testOrder->where('trade_no', $testTradeNo)->delete();

                return json([
                    'code' => 0,
                    'msg' => '二维码生成失败'
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '二维码生成测试异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试媒体上传
     */
    public function testMediaUpload()
    {
        try {
            $wechatWorkService = new \app\common\service\WechatWorkService();

            // 创建一个测试图片
            $testImagePath = runtime_path() . 'temp/test_upload_' . time() . '.png';
            $dir = dirname($testImagePath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            // 创建简单的测试图片
            $image = imagecreate(100, 100);
            $white = imagecolorallocate($image, 255, 255, 255);
            $black = imagecolorallocate($image, 0, 0, 0);
            imagefill($image, 0, 0, $white);
            imagestring($image, 5, 20, 40, 'TEST', $black);
            imagepng($image, $testImagePath);
            imagedestroy($image);

            // 测试上传
            $mediaId = $wechatWorkService->uploadMedia($testImagePath);

            // 清理测试文件
            if (file_exists($testImagePath)) {
                unlink($testImagePath);
            }

            if ($mediaId) {
                return json([
                    'code' => 1,
                    'msg' => '媒体上传测试通过',
                    'data' => [
                        'media_id' => $mediaId
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '媒体上传失败，请检查企业微信配置和网络连接'
                ]);
            }

        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '媒体上传测试异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取支付统计
     */
    public function getStats()
    {
        try {
            $today = date('Y-m-d');
            $yesterday = date('Y-m-d', strtotime('-1 day'));

            // 今日统计
            $todayStats = SystemOrder::where('wechat_work_user', '<>', '')
                ->where('create_time', '>=', strtotime($today))
                ->field('count(*) as total, sum(case when status=1 then 1 else 0 end) as success, sum(case when status=1 then money else 0 end) as amount')
                ->find();

            // 昨日统计
            $yesterdayStats = SystemOrder::where('wechat_work_user', '<>', '')
                ->where('create_time', '>=', strtotime($yesterday))
                ->where('create_time', '<', strtotime($today))
                ->field('count(*) as total, sum(case when status=1 then 1 else 0 end) as success, sum(case when status=1 then money else 0 end) as amount')
                ->find();

            return json([
                'code' => 1,
                'data' => [
                    'today' => [
                        'total' => $todayStats['total'] ?? 0,
                        'success' => $todayStats['success'] ?? 0,
                        'amount' => $todayStats['amount'] ?? 0,
                        'success_rate' => $todayStats['total'] > 0 ? round($todayStats['success'] / $todayStats['total'] * 100, 2) : 0
                    ],
                    'yesterday' => [
                        'total' => $yesterdayStats['total'] ?? 0,
                        'success' => $yesterdayStats['success'] ?? 0,
                        'amount' => $yesterdayStats['amount'] ?? 0,
                        'success_rate' => $yesterdayStats['total'] > 0 ? round($yesterdayStats['success'] / $yesterdayStats['total'] * 100, 2) : 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 诊断企业微信配置和网络问题
     */
    public function diagnose()
    {
        try {
            $result = [
                'server_ip' => $this->getServerIP(),
                'config_check' => $this->checkWechatWorkConfig(),
                'network_check' => $this->checkNetworkConnectivity(),
                'api_test' => $this->testWechatWorkAPI(),
                'suggestions' => []
            ];

            // 生成建议
            if (!$result['config_check']['valid']) {
                $result['suggestions'][] = '请完善企业微信配置信息';
            }

            if (!$result['network_check']['can_access_api']) {
                $result['suggestions'][] = '服务器无法访问企业微信API，请检查网络连接';
            }

            if (isset($result['api_test']['error_code']) && $result['api_test']['error_code'] == 60020) {
                $result['suggestions'][] = '当前服务器IP(' . $result['server_ip'] . ')不在企业微信应用白名单中';
                $result['suggestions'][] = '请在企业微信管理后台 -> 应用管理 -> 选择对应应用 -> 网络配置 中添加此IP到白名单';
            }

            return json(['code' => 1, 'data' => $result]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '诊断失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取服务器IP地址
     */
    private function getServerIP()
    {
        // 尝试多种方式获取服务器外网IP
        $ip = '';

        // 方法1: 通过外部服务获取
        $services = [
            'https://api.ipify.org',
            'https://ipinfo.io/ip',
            'https://icanhazip.com'
        ];

        foreach ($services as $service) {
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $service);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 5);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                $response = curl_exec($ch);
                curl_close($ch);

                if ($response && filter_var(trim($response), FILTER_VALIDATE_IP)) {
                    $ip = trim($response);
                    break;
                }
            } catch (\Exception $e) {
                continue;
            }
        }

        // 方法2: 如果外部服务失败，使用本地IP
        if (empty($ip)) {
            $ip = $_SERVER['SERVER_ADDR'] ?? gethostbyname(gethostname());
        }

        return $ip;
    }

    /**
     * 检查企业微信配置
     */
    private function checkWechatWorkConfig()
    {
        $corpId = get_setting('wechat_work_corp_id');
        $corpSecret = get_setting('wechat_work_corp_secret');
        $agentId = get_setting('wechat_work_agent_id');
        $token = get_setting('wechat_work_token');
        $encodingAESKey = get_setting('wechat_work_encoding_aes_key');
        $enabled = get_setting('wechat_work_enabled');

        $result = [
            'enabled' => $enabled == 1,
            'corp_id' => !empty($corpId),
            'corp_secret' => !empty($corpSecret),
            'agent_id' => !empty($agentId),
            'token' => !empty($token),
            'encoding_aes_key' => !empty($encodingAESKey),
            'valid' => false,
            'details' => []
        ];

        // 详细验证企业ID格式
        if (!empty($corpId)) {
            if (preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $corpId)) {
                $result['details']['corp_id'] = '格式正确';
            } else {
                $result['details']['corp_id'] = '格式错误：应为ww开头的16位字符串';
                $result['corp_id'] = false;
            }
        } else {
            $result['details']['corp_id'] = '未配置';
        }

        // 详细验证应用Secret
        if (!empty($corpSecret)) {
            if (strlen($corpSecret) >= 20) {
                $result['details']['corp_secret'] = '长度正确';
            } else {
                $result['details']['corp_secret'] = '长度不足：应至少20位字符';
                $result['corp_secret'] = false;
            }
        } else {
            $result['details']['corp_secret'] = '未配置';
        }

        // 详细验证应用ID
        if (!empty($agentId)) {
            if (is_numeric($agentId) && $agentId > 0) {
                $result['details']['agent_id'] = '格式正确';
            } else {
                $result['details']['agent_id'] = '格式错误：应为正整数';
                $result['agent_id'] = false;
            }
        } else {
            $result['details']['agent_id'] = '未配置';
        }

        // 详细验证Token
        if (!empty($token)) {
            if (strlen($token) >= 3) {
                $result['details']['token'] = '长度正确';
            } else {
                $result['details']['token'] = '长度不足：应至少3位字符';
                $result['token'] = false;
            }
        } else {
            $result['details']['token'] = '未配置';
        }

        // 详细验证EncodingAESKey
        if (!empty($encodingAESKey)) {
            if (strlen($encodingAESKey) == 43) {
                $result['details']['encoding_aes_key'] = '长度正确';
            } else {
                $result['details']['encoding_aes_key'] = '长度错误：应为43位字符';
                $result['encoding_aes_key'] = false;
            }
        } else {
            $result['details']['encoding_aes_key'] = '未配置';
        }

        // 检查启用状态
        if ($enabled == 1) {
            $result['details']['enabled'] = '已启用';
        } else {
            $result['details']['enabled'] = '未启用';
        }

        // 综合判断配置是否有效
        $result['valid'] = $result['enabled'] && $result['corp_id'] && $result['corp_secret'] && $result['agent_id'] && $result['token'] && $result['encoding_aes_key'];

        return $result;
    }

    /**
     * 验证企业微信配置格式
     */
    public function validateConfig()
    {
        try {
            $corpId = input('corp_id', '');
            $corpSecret = input('corp_secret', '');
            $agentId = input('agent_id', '');
            $token = input('token', '');
            $encodingAESKey = input('encoding_aes_key', '');

            $errors = [];
            $warnings = [];

            // 验证企业ID
            if (empty($corpId)) {
                $errors[] = '企业ID不能为空';
            } elseif (!preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $corpId)) {
                $errors[] = '企业ID格式错误，应为ww开头的16位字符串';
            }

            // 验证应用Secret
            if (empty($corpSecret)) {
                $errors[] = '应用Secret不能为空';
            } elseif (strlen($corpSecret) < 20) {
                $warnings[] = '应用Secret长度可能不足，建议检查是否完整';
            }

            // 验证应用ID
            if (empty($agentId)) {
                $errors[] = '应用ID不能为空';
            } elseif (!is_numeric($agentId) || $agentId <= 0) {
                $errors[] = '应用ID格式错误，应为正整数';
            }

            // 验证Token
            if (empty($token)) {
                $errors[] = 'Token不能为空';
            } elseif (strlen($token) < 3) {
                $warnings[] = 'Token长度可能不足，建议检查是否完整';
            }

            // 验证EncodingAESKey
            if (empty($encodingAESKey)) {
                $errors[] = 'EncodingAESKey不能为空';
            } elseif (strlen($encodingAESKey) != 43) {
                $errors[] = 'EncodingAESKey长度错误，应为43位字符';
            }

            $result = [
                'valid' => empty($errors),
                'errors' => $errors,
                'warnings' => $warnings
            ];

            // 如果基础验证通过，尝试连接测试
            if ($result['valid']) {
                $testResult = $this->testConfigConnection($corpId, $corpSecret, $agentId, $token, $encodingAESKey);
                $result['connection_test'] = $testResult;

                if (!$testResult['success']) {
                    $result['valid'] = false;
                    $result['errors'][] = '连接测试失败：' . $testResult['error'];
                }
            }

            return json(['code' => 1, 'data' => $result]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '验证失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 测试配置连接
     */
    private function testConfigConnection($corpId, $corpSecret, $agentId, $token, $encodingAESKey)
    {
        try {
            // 临时保存原配置
            $originalCorpId = get_setting('wechat_work_corp_id');
            $originalCorpSecret = get_setting('wechat_work_corp_secret');
            $originalAgentId = get_setting('wechat_work_agent_id');
            $originalToken = get_setting('wechat_work_token');
            $originalEncodingAESKey = get_setting('wechat_work_encoding_aes_key');

            // 临时设置新配置
            set_setting('wechat_work_corp_id', $corpId);
            set_setting('wechat_work_corp_secret', $corpSecret);
            set_setting('wechat_work_agent_id', $agentId);
            set_setting('wechat_work_token', $token);
            set_setting('wechat_work_encoding_aes_key', $encodingAESKey);

            // 创建服务实例测试
            $testService = new \app\common\service\WechatWorkService();
            $accessToken = $testService->getAccessToken();

            // 恢复原配置
            set_setting('wechat_work_corp_id', $originalCorpId);
            set_setting('wechat_work_corp_secret', $originalCorpSecret);
            set_setting('wechat_work_agent_id', $originalAgentId);
            set_setting('wechat_work_token', $originalToken);
            set_setting('wechat_work_encoding_aes_key', $originalEncodingAESKey);

            if ($accessToken) {
                return [
                    'success' => true,
                    'token_preview' => substr($accessToken, 0, 10) . '...'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => '无法获取访问令牌，请检查配置是否正确'
                ];
            }

        } catch (\Exception $e) {
            // 恢复原配置
            if (isset($originalCorpId)) {
                set_setting('wechat_work_corp_id', $originalCorpId);
                set_setting('wechat_work_corp_secret', $originalCorpSecret);
                set_setting('wechat_work_agent_id', $originalAgentId);
                set_setting('wechat_work_token', $originalToken);
                set_setting('wechat_work_encoding_aes_key', $originalEncodingAESKey);
            }

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查网络连接
     */
    private function checkNetworkConnectivity()
    {
        $result = [
            'can_access_api' => false,
            'response_time' => 0
        ];

        try {
            $start = microtime(true);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://qyapi.weixin.qq.com');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $result['response_time'] = round((microtime(true) - $start) * 1000, 2);
            $result['can_access_api'] = $httpCode > 0;

        } catch (\Exception $e) {
            $result['error'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * 测试企业微信API
     */
    private function testWechatWorkAPI()
    {
        try {
            $wechatWorkService = new \app\common\service\WechatWorkService();
            $token = $wechatWorkService->getAccessToken();

            if ($token) {
                return [
                    'success' => true,
                    'token_preview' => substr($token, 0, 10) . '...'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => '获取access_token失败'
                ];
            }
        } catch (\Exception $e) {
            // 尝试解析错误信息
            $errorMsg = $e->getMessage();
            $result = ['success' => false, 'error' => $errorMsg];

            // 检查是否包含企业微信API错误
            if (preg_match('/\{"errcode":(\d+),"errmsg":"([^"]+)"\}/', $errorMsg, $matches)) {
                $result['error_code'] = intval($matches[1]);
                $result['error_message'] = $matches[2];
            }

            return $result;
        }
    }

    /**
     * 测试企业微信客服事件处理
     */
    public function testCustomerServiceEvent()
    {
        try {
            $sessionId = input('session_id', 'pay_2025082109081349626_1755760052');
            $openKfId = input('open_kfid', 'wkekC8CgAAcgdQ6vpT7Dn0B31dl9nXkw');
            $token = input('token', 'ENCSBGRknQW8X31GPDRbpP6kg');
            
            // 构建模拟的客服事件XML
            $xml = "<xml>
                <ToUserName><![CDATA[toUser]]></ToUserName>
                <FromUserName><![CDATA[fromUser]]></FromUserName>
                <CreateTime>" . time() . "</CreateTime>
                <MsgType><![CDATA[event]]></MsgType>
                <Event><![CDATA[kf_msg_or_event]]></Event>
                <Token><![CDATA[{$token}]]></Token>
                <OpenKfId><![CDATA[{$openKfId}]]></OpenKfId>
                <State><![CDATA[{$sessionId}]]></State>
            </xml>";
            
            Log::info('测试企业微信客服事件: ' . $xml);
            
            // 解析XML消息
            $message = $this->parseXmlMessage($xml);
            if (!$message) {
                Log::error('测试企业微信客服事件XML解析失败');
                return json(['code' => 0, 'msg' => 'XML解析失败']);
            }
            
            Log::info('测试企业微信客服事件解析成功: ' . json_encode($message));
            
            // 处理消息
            $response = $this->wechatWorkService->handleMessage($message);
            
            Log::info('测试企业微信客服事件处理结果: ' . json_encode($response));
            
            return json([
                'code' => 1,
                'msg' => '测试成功',
                'data' => [
                    'message' => $message,
                    'response' => $response
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('测试企业微信客服事件失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '测试失败: ' . $e->getMessage()]);
        }
    }
}
