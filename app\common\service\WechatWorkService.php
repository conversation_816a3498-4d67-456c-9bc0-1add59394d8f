<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;
use callback\WXBizMsgCrypt;

/**
 * 企业微信客服服务
 * 实现企业微信客服自动回复二维码功能
 */
class WechatWorkService
{
    /**
     * 企业微信配置
     */
    private $corpId;
    private $corpSecret;
    private $agentId;
    private $token;
    private $encodingAESKey;
    private $accessToken;
    private $openKfId; // 企业微信客服账号ID

    public function __construct()
    {
        $this->corpId = get_setting('wechat_work_corp_id');
        $this->corpSecret = get_setting('wechat_work_corp_secret');
        $this->agentId = get_setting('wechat_work_agent_id');
        $this->token = get_setting('wechat_work_token');
        $this->encodingAESKey = get_setting('wechat_work_encoding_aes_key');
        $this->openKfId = get_setting('wechat_work_open_kf_id');
    }

    /**
     * 获取企业微信访问令牌
     *
     * @return string|false
     */
    /**
     * 解密企业微信消息
     *
     * @param string $msgSignature 消息签名
     * @param string $timestamp 时间戳
     * @param string $nonce 随机数
     * @param string $encryptMsg 加密消息
     * @return array 解密后的消息数组
     */
    public function decryptMessage(string $msgSignature, string $timestamp, string $nonce, string $encryptMsg): array
    {
        try {
            // 检查加密消息是否为空
            if (empty($encryptMsg)) {
                Log::error('企业微信消息解密失败：加密消息为空');
                return [];
            }

            // 引入企业微信加密库
            $cryptFile = root_path() . 'extend/callback/WXBizMsgCrypt.php';
            if (file_exists($cryptFile)) {
                // 加载所有必需的类
                require_once root_path() . 'extend/callback/ErrorCode.php';
                require_once root_path() . 'extend/callback/SHA1.php';
                require_once root_path() . 'extend/callback/XMLParse.php';
                require_once root_path() . 'extend/callback/PKCS7Encoder.php';
                require_once root_path() . 'extend/callback/Prpcrypt.php';
                require_once $cryptFile;

                if (class_exists('callback\\WXBizMsgCrypt')) {
                    // 记录解密参数用于调试
                    Log::info('企业微信消息解密参数: msgSignature=' . $msgSignature .
                             ', timestamp=' . $timestamp .
                             ', nonce=' . $nonce .
                             ', token=' . ($this->token ? '已设置' : '未设置') .
                             ', encodingAESKey=' . ($this->encodingAESKey ? '已设置' : '未设置') .
                             ', corpId=' . ($this->corpId ? '已设置' : '未设置'));

                    // 检查配置是否完整
                    if (empty($this->token) || empty($this->encodingAESKey) || empty($this->corpId)) {
                        Log::error('企业微信解密配置不完整: token=' . ($this->token ? '已设置' : '未设置') .
                                  ', encodingAESKey=' . ($this->encodingAESKey ? '已设置' : '未设置') .
                                  ', corpId=' . ($this->corpId ? '已设置' : '未设置'));
                        return [];
                    }

                    // 详细记录配置信息用于调试
                    Log::info('企业微信解密配置详情: token长度=' . strlen($this->token) .
                             ', encodingAESKey长度=' . strlen($this->encodingAESKey) .
                             ', corpId=' . $this->corpId);

                    // 验证配置格式
                    if (strlen($this->encodingAESKey) !== 43) {
                        Log::error('企业微信EncodingAESKey长度错误，应为43位，当前为' . strlen($this->encodingAESKey) . '位');
                        return [];
                    }

                    if (!preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $this->corpId)) {
                        Log::error('企业微信CorpId格式错误，应以ww开头，当前为: ' . $this->corpId);
                        return [];
                    }

                    $wxCrypt = new \callback\WXBizMsgCrypt($this->token, $this->encodingAESKey, $this->corpId);

                    // 检查是否是XML格式
                    if (strpos($encryptMsg, '<xml>') === 0 || strpos($encryptMsg, '<?xml') === 0) {
                        // 已经是XML格式，直接解析
                        $xmlTree = new \DOMDocument();
                        libxml_use_internal_errors(true); // 禁用XML错误报告，避免抛出异常

                        // 检查XML内容是否为空
                        if (empty(trim($encryptMsg))) {
                            Log::error('企业微信消息XML内容为空');
                            return [];
                        }

                        // 再次检查XML内容是否为空，防止trim后的空字符串
                        if (strlen(trim($encryptMsg)) === 0) {
                            Log::error('企业微信消息XML内容为空（长度检查）');
                            return [];
                        }

                        $loadResult = $xmlTree->loadXML($encryptMsg);
                        libxml_clear_errors(); // 清除XML错误

                        // 检查XML是否成功加载
                        if (!$loadResult) {
                            Log::error('企业微信消息XML解析失败');
                            return [];
                        }

                        $arrayEncrypt = $xmlTree->getElementsByTagName('Encrypt');
                        if ($arrayEncrypt->length > 0) {
                            $encryptNode = $arrayEncrypt->item(0);
                            if ($encryptNode !== null) {
                                $encrypt = $encryptNode->nodeValue;
                                if (empty($encrypt)) {
                                    Log::error('企业微信消息中Encrypt节点内容为空');
                                    return [];
                                }
                            } else {
                                Log::error('企业微信消息中Encrypt节点为null');
                                return [];
                            }
                        } else {
                            Log::error('企业微信消息中未找到Encrypt节点');
                            return [];
                        }
                    } else {
                        // 不是XML格式，可能是直接传递的加密内容
                        // 检查加密内容是否为空
                        if (empty(trim($encryptMsg))) {
                            Log::error('企业微信消息加密内容为空');
                            return [];
                        }

                        // 再次检查加密内容是否为空，防止trim后的空字符串
                        if (strlen(trim($encryptMsg)) === 0) {
                            Log::error('企业微信消息加密内容为空（长度检查）');
                            return [];
                        }

                        $encrypt = $encryptMsg;
                    }

                    $format = "<xml><ToUserName><![CDATA[toUser]]></ToUserName><Encrypt><![CDATA[%s]]></Encrypt></xml>";
                    $fromXML = sprintf($format, $encrypt);

                    $decryptMsg = "";
                    $errCode = $wxCrypt->DecryptMsg($msgSignature, $timestamp, $nonce, $fromXML, $decryptMsg);
                    if ($errCode == 0) {
                        // 解密成功
                        Log::info('企业微信消息解密成功，原始数据: ' . $decryptMsg);

                        // 检查解密后的消息是否为空
                        if (empty(trim($decryptMsg))) {
                            Log::error('企业微信消息解密后内容为空');
                            return [];
                        }

                        $data = simplexml_load_string($decryptMsg, 'SimpleXMLElement', LIBXML_NOCDATA);
                        if ($data) {
                            $message = [
                                'ToUserName' => (string)$data->ToUserName,
                                'FromUserName' => (string)$data->FromUserName,
                                'CreateTime' => (string)$data->CreateTime,
                                'MsgType' => (string)$data->MsgType,
                                'Content' => (string)$data->Content,
                                'MsgId' => (string)$data->MsgId,
                                'AgentID' => (string)$data->AgentID,
                                'Event' => (string)$data->Event,
                                'Token' => (string)$data->Token,
                                'OpenKfId' => (string)$data->OpenKfId,
                            ];

                            Log::info('企业微信消息解析成功: ' . json_encode($message));
                            return $message;
                        } else {
                            Log::error('企业微信消息XML解析失败，解密后的内容: ' . $decryptMsg);
                        }
                    } else {
                        Log::error('企业微信消息解密失败，错误码: ' . $errCode);

                        // 详细的错误分析
                        $this->analyzeDecryptError($errCode, $msgSignature, $timestamp, $nonce, $encrypt);
                    }
                }
            } else {
                Log::error('企业微信加密库文件不存在: ' . $cryptFile);
            }

            // 如果解密失败，返回空数组
            return [];
        } catch (\Exception $e) {
            Log::error('解密企业微信消息失败: ' . $e->getMessage());
            return [];
        }
    }

    public function getAccessToken()
    {
        $cacheKey = 'wechat_work_access_token';
        $token = Cache::get($cacheKey);

        if (!$token) {
            $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={$this->corpId}&corpsecret={$this->corpSecret}";

            $response = $this->httpGet($url);
            $result = json_decode($response, true);

            if (isset($result['access_token'])) {
                $token = $result['access_token'];
                // 缓存1小时50分钟（企业微信token有效期2小时）
                Cache::set($cacheKey, $token, 6600);
            } else {
                Log::error('获取企业微信access_token失败: ' . $response);
                return false;
            }
        }

        return $token;
    }

    /**
     * 发送文本消息
     *
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @return bool
     */
    public function sendTextMessage(string $userId, string $content, bool $useRetry = true): bool
    {
        $messageData = [
            'msgtype' => 'text',
            'text' => [
                'content' => $content
            ]
        ];

        if ($useRetry) {
            return $this->sendMessageWithRetry($userId, $messageData, 3);
        } else {
            return $this->sendMessage($userId, $messageData);
        }
    }

    /**
     * 发送图片消息（二维码）
     *
     * @param string $userId 用户ID
     * @param string $mediaId 媒体文件ID
     * @return bool
     */
    public function sendImageMessage(string $userId, string $mediaId, bool $useRetry = true): bool
    {
        $messageData = [
            'msgtype' => 'image',
            'image' => [
                'media_id' => $mediaId
            ]
        ];

        if ($useRetry) {
            return $this->sendMessageWithRetry($userId, $messageData, 3);
        } else {
            return $this->sendMessage($userId, $messageData);
        }
    }

    /**
     * 发送图文消息
     *
     * @param string $userId 用户ID
     * @param array $articles 图文文章数组
     * @return bool
     */
    public function sendNewsMessage(string $userId, array $articles, bool $useRetry = true): bool
    {
        $messageData = [
            'msgtype' => 'news',
            'news' => [
                'articles' => $articles
            ]
        ];

        if ($useRetry) {
            return $this->sendMessageWithRetry($userId, $messageData, 3);
        } else {
            return $this->sendMessage($userId, $messageData);
        }
    }

    /**
     * 发送文件消息
     *
     * @param string $userId 用户ID
     * @param string $mediaId 媒体文件ID
     * @return bool
     */
    public function sendFileMessage(string $userId, string $mediaId, bool $useRetry = true): bool
    {
        $messageData = [
            'msgtype' => 'file',
            'file' => [
                'media_id' => $mediaId
            ]
        ];

        if ($useRetry) {
            return $this->sendMessageWithRetry($userId, $messageData, 3);
        } else {
            return $this->sendMessage($userId, $messageData);
        }
    }

    /**
     * 发送卡片消息
     *
     * @param string $userId 用户ID
     * @param array $card 卡片内容
     * @return bool
     */
    public function sendCardMessage(string $userId, array $card, bool $useRetry = true): bool
    {
        $messageData = [
            'msgtype' => 'textcard',
            'textcard' => $card
        ];

        if ($useRetry) {
            return $this->sendMessageWithRetry($userId, $messageData, 3);
        } else {
            return $this->sendMessage($userId, $messageData);
        }
    }

    /**
     * 发送markdown消息
     *
     * @param string $userId 用户ID
     * @param string $content markdown内容
     * @return bool
     */
    public function sendMarkdownMessage(string $userId, string $content, bool $useRetry = true): bool
    {
        $messageData = [
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content
            ]
        ];

        if ($useRetry) {
            return $this->sendMessageWithRetry($userId, $messageData, 3);
        } else {
            return $this->sendMessage($userId, $messageData);
        }
    }

    /**
     * 上传临时素材
     *
     * @param string $filePath 文件路径
     * @param string $type 媒体文件类型（image、voice、video、file）
     * @return string|false 返回media_id或false
     */
    public function uploadMedia(string $filePath, string $type = 'image')
    {
        try {
            // 检查文件是否存在
            if (!file_exists($filePath)) {
                Log::error('上传文件不存在: ' . $filePath);
                return false;
            }

            // 检查文件大小（企业微信限制图片不超过2MB）
            $fileSize = filesize($filePath);
            if ($fileSize > 2 * 1024 * 1024) {
                Log::error('上传文件过大: ' . $fileSize . ' bytes');
                return false;
            }

            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::error('获取企业微信access_token失败');
                return false;
            }

            $url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={$accessToken}&type={$type}";

            $data = [
                'media' => new \CURLFile($filePath, 'image/png', basename($filePath))
            ];

            $response = $this->httpPost($url, $data, true);
            $result = json_decode($response, true);

            if (isset($result['media_id'])) {
                Log::info('企业微信媒体文件上传成功: ' . $result['media_id']);
                return $result['media_id'];
            } else {
                // 详细的错误处理
                $errorCode = $result['errcode'] ?? 'unknown';
                $errorMsg = $result['errmsg'] ?? 'unknown error';

                // 特殊处理IP白名单问题
                if ($errorCode == 60020) {
                    // 从错误信息中提取IP地址
                    if (preg_match('/from ip\s*:\s*([\d\.]+)/', $response, $matches)) {
                        $serverIP = $matches[1];
                        Log::error('企业微信IP白名单限制: 当前服务器IP(' . $serverIP . ')不在企业微信应用的可信IP列表中。错误详情: ' . $response);
                        Log::error('解决方案: 请在企业微信管理后台 -> 应用管理 -> 选择对应应用 -> 网络配置 中添加以下IP到白名单: ' . $serverIP);
                    } else {
                        Log::error('企业微信IP白名单限制: 当前服务器IP不在企业微信应用的可信IP列表中。错误详情: ' . $response);
                        Log::error('解决方案: 请在企业微信管理后台 -> 应用管理 -> 选择对应应用 -> 网络配置 中添加服务器IP到白名单');
                    }
                } else {
                    Log::error("上传企业微信媒体文件失败: 错误码{$errorCode}, 错误信息: {$errorMsg}");
                }

                return false;
            }
        } catch (\Exception $e) {
            Log::error('上传企业微信媒体文件异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成企业微信客服链接并自动发送支付消息
     *
     * @param string $tradeNo 订单号
     * @param float $amount 支付金额
     * @param array $orderInfo 订单信息
     * @return array
     */
    public function generateCustomerServiceLink(string $tradeNo, float $amount, array $orderInfo = []): array
    {
        try {
            // 检查企业微信配置
            if (empty($this->corpId) || empty($this->corpSecret) || empty($this->agentId)) {
                Log::error('企业微信配置不完整: corpId=' . $this->corpId . ', agentId=' . $this->agentId);
                return ['success' => false, 'message' => '企业微信配置不完整，请检查配置'];
            }

            // 生成唯一的客服会话ID
            $sessionId = 'pay_' . $tradeNo . '_' . time();

            // 创建临时用户标识（用于客服会话）
            $tempUserId = 'temp_user_' . md5($tradeNo . time());

            // 生成支付二维码
            $qrCodePath = $this->generatePaymentQRCode($tradeNo, $amount);
            if (!$qrCodePath) {
                Log::error('生成支付二维码失败: 订单号=' . $tradeNo);
                return ['success' => false, 'message' => '生成支付二维码失败，请检查订单信息'];
            }

            // 上传二维码到企业微信
            $mediaId = $this->uploadMedia($qrCodePath);
            if (!$mediaId) {
                // 清理临时文件
                if (file_exists($qrCodePath)) {
                    unlink($qrCodePath);
                }
                return ['success' => false, 'message' => '上传二维码到企业微信失败，请检查网络连接和企业微信配置'];
            }

            // 生成企业微信客服链接
            $customerServiceUrl = $this->buildCustomerServiceUrl($sessionId, $tempUserId);
            if (empty($customerServiceUrl)) {
                // 清理临时文件
                if (file_exists($qrCodePath)) {
                    unlink($qrCodePath);
                }
                Log::error('生成企业微信客服链接失败: 客服链接为空，请检查企业微信客服功能是否已开启');

                // 检查最新的日志，获取更详细的错误信息
                $logFile = runtime_path() . 'log/' . date('Ym') . '/' . date('d') . '.log';
                if (file_exists($logFile)) {
                    $logContent = file_get_contents($logFile);
                    if (preg_match('/生成企业微信客服链接失败: 错误码=(\d+), 错误信息=([^,]+)/', $logContent, $matches)) {
                        $errorCode = $matches[1];
                        $errorMsg = trim($matches[2]);

                        // 根据错误码提供更具体的解决方案
                        if ($errorCode == 40058) {
                            return ['success' => false, 'message' => '企业微信客服账号ID未配置，请在系统设置中配置正确的客服账号ID(open_kfid)'];
                        } elseif ($errorCode == 48002) {
                            return ['success' => false, 'message' => '企业微信客服功能未开启，请在企业微信管理后台开启客服功能'];
                        } elseif ($errorCode == 60020) {
                            return ['success' => false, 'message' => '服务器IP不在企业微信白名单中，请在企业微信管理后台添加服务器IP到白名单'];
                        } elseif ($errorCode == 301024) {
                            return ['success' => false, 'message' => '企业微信客服账号不存在或已禁用，请检查客服账号配置'];
                        } elseif ($errorCode == 301025) {
                            return ['success' => false, 'message' => '企业微信客服账号未配置接待人员，请为客服账号配置接待人员'];
                        } elseif ($errorCode == 301026) {
                            return ['success' => false, 'message' => '企业微信客服账号接待人员不在线，请确保接待人员在线'];
                        } else {
                            return ['success' => false, 'message' => "生成客服链接失败: {$errorMsg} (错误码: {$errorCode})"];
                        }
                    }
                }

                return ['success' => false, 'message' => '生成客服链接失败，请检查企业微信客服功能是否已开启'];
            }

            // 预设支付消息内容
            $paymentMessage = $this->buildPaymentMessage($tradeNo, $amount, $orderInfo);

            // 将消息和二维码信息缓存，等待用户访问时发送
            $cacheKey = 'wechat_service_' . $sessionId;
            $serviceData = [
                'trade_no' => $tradeNo,
                'amount' => $amount,
                'media_id' => $mediaId,
                'message' => $paymentMessage,
                'temp_user_id' => $tempUserId,
                'create_time' => time(),
                'customer_service_url' => $customerServiceUrl
            ];
            cache($cacheKey, $serviceData, 1800); // 缓存30分钟
            
            // 更新客服会话列表缓存，用于在客服事件中查找对应的会话
            $this->updateServiceSessionList($sessionId, $serviceData);

            // 清理临时文件
            if (file_exists($qrCodePath)) {
                unlink($qrCodePath);
            }

            Log::info('企业微信客服链接生成成功: ' . $sessionId . ', 链接: ' . $customerServiceUrl);
            return [
                'success' => true,
                'customer_service_url' => $customerServiceUrl,
                'session_id' => $sessionId,
                'message' => '客服链接生成成功，请复制链接到企业微信中打开'
            ];

        } catch (\Exception $e) {
            Log::error('生成企业微信客服链接失败: ' . $e->getMessage() . ', 堆栈: ' . $e->getTraceAsString());
            return ['success' => false, 'message' => '生成客服链接失败: ' . $e->getMessage()];
        }
    }

    /**
     * 构建企业微信客服链接
     *
     * @param string $sessionId 会话ID
     * @param string $tempUserId 临时用户ID
     * @return string
     */
    public function buildCustomerServiceUrl(string $sessionId, string $tempUserId): string
    {
        try {
            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::error('获取企业微信access_token失败');
                return '';
            }

            // 自动获取可用的客服账号ID
            $openKfId = $this->getAvailableCustomerServiceAccountId();
            if (empty($openKfId)) {
                Log::error('无法获取企业微信客服账号ID(open_kfid)，请检查企业微信客服功能是否已开启');
                return '';
            }

            // 调用企业微信API生成客服链接
            // 使用正确的API端点：添加客服联系方式
            $url = "https://qyapi.weixin.qq.com/cgi-bin/kf/add_contact_way?access_token={$accessToken}";

            // 根据企业微信官方文档构建参数
            // 添加必需的open_kfid参数
            $data = [
                'open_kfid' => $openKfId, // 企业微信客服账号ID，必需参数
                'type' => 1, // 1-企业微信客服
                'scene' => 1, // 1-小程序场景
                'style' => 1, // 1-普通样式
                'remark' => '支付客服-' . $sessionId,
                'skip_verify' => true, // 跳过验证
                'state' => $sessionId,
            ];

            $jsonData = json_encode($data);
            Log::info('企业微信客服链接API请求: URL=' . $url . ', 数据=' . $jsonData);

            $response = $this->httpPost($url, $jsonData);
            Log::info('企业微信客服链接API原始响应: ' . $response);

            // 检查响应是否为空
            if (empty($response)) {
                Log::error('企业微信客服链接API响应为空，可能是网络问题或API服务不可用');
                return '';
            }

            $result = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('企业微信客服链接API响应JSON解析失败: ' . json_last_error_msg() . ', 原始响应: ' . $response);
                return '';
            }

            Log::info('企业微信客服链接API解析后响应: ' . json_encode($result));

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                // 返回生成的客服链接
                $customerServiceUrl = $result['url'] ?? '';
                Log::info('企业微信客服链接生成成功: ' . $customerServiceUrl);
                return $customerServiceUrl;
            } else {
                $errorCode = $result['errcode'] ?? 'unknown';
                $errorMsg = $result['errmsg'] ?? 'unknown error';
                Log::error('生成企业微信客服链接失败: 错误码=' . $errorCode . ', 错误信息=' . $errorMsg . ', 完整响应=' . $response);

                // 特殊处理常见错误
                if ($errorCode == 60020) {
                    Log::error('企业微信IP白名单限制: 当前服务器IP不在企业微信应用的可信IP列表中。');
                    Log::error('解决方案: 请在企业微信管理后台 -> 应用管理 -> 选择对应应用 -> 网络配置 中添加服务器IP到白名单');
                } elseif ($errorCode == 45009) {
                    Log::error('企业微信API调用频率限制: 请稍后重试');
                } elseif ($errorCode == 40014) {
                    Log::error('企业微信access_token无效: 尝试重新获取');
                    // 清除缓存中的access_token
                    Cache::delete('wechat_work_access_token');
                } elseif ($errorCode == 40058) {
                    Log::error('企业微信客服API参数错误: 缺少必需的open_kfid参数或其他参数错误');
                    Log::error('解决方案1: 请在企业微信管理后台 -> 客服管理 中获取正确的open_kfid，并在系统设置中配置wechat_work_open_kf_id');
                    Log::error('解决方案2: 请检查API请求参数是否符合企业微信官方文档要求');
                } elseif ($errorCode == 48002) {
                    Log::error('企业微信客服功能未开启或API接口无权限: 请在企业微信管理后台开启客服功能');
                    Log::error('解决方案1: 请在企业微信管理后台 -> 应用管理 -> 客服 -> 开启客服功能');
                    Log::error('解决方案2: 请检查自建应用是否有权限调用客服API，可能需要联系企业微信管理员开通权限');
                    Log::error('解决方案3: 确认企业微信版本是否支持客服API功能，部分版本可能需要升级');
                } elseif ($errorCode == 301024) {
                    Log::error('企业微信客服账号不存在或已禁用: 请检查客服账号配置');
                    Log::error('解决方案: 请在企业微信管理后台 -> 客服管理 中检查客服账号状态');
                } elseif ($errorCode == 301025) {
                    Log::error('企业微信客服账号未配置接待人员: 请为客服账号配置接待人员');
                    Log::error('解决方案: 请在企业微信管理后台 -> 客服管理 -> 选择客服账号 -> 接待人员 中添加接待人员');
                } elseif ($errorCode == 301026) {
                    Log::error('企业微信客服账号接待人员不在线: 请确保接待人员在线');
                    Log::error('解决方案: 请确保客服接待人员已登录企业微信并保持在线状态');
                } elseif ($errorCode == 301027) {
                    Log::error('企业微信客服账号不存在: 请检查客服账号配置');
                    Log::error('解决方案: 请在企业微信管理后台 -> 客服管理 中检查客服账号是否存在');
                }

                return '';
            }

        } catch (\Exception $e) {
            Log::error('生成企业微信客服链接异常: ' . $e->getMessage() . ', 堆栈: ' . $e->getTraceAsString());
            return '';
        }
    }

    /**
     * 获取企业微信客服账号列表
     *
     * @return array|false 返回客服账号列表或false
     */
    public function getCustomerServiceAccountList()
    {
        try {
            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::error('获取企业微信access_token失败');
                return false;
            }

            // 调用企业微信API获取客服账号列表
            $url = "https://qyapi.weixin.qq.com/cgi-bin/kf/account/list?access_token={$accessToken}";

            Log::info('企业微信客服账号列表API请求: URL=' . $url);

            $response = $this->httpGet($url);
            Log::info('企业微信客服账号列表API原始响应: ' . $response);

            // 检查响应是否为空
            if (empty($response)) {
                Log::error('企业微信客服账号列表API响应为空，可能是网络问题或API服务不可用');
                return false;
            }

            $result = json_decode($response, true);

            // 检查JSON解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('企业微信客服账号列表API响应JSON解析失败: ' . json_last_error_msg() . ', 原始响应: ' . $response);
                return false;
            }

            Log::info('企业微信客服账号列表API解析后响应: ' . json_encode($result));

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                // 返回客服账号列表
                $accountList = $result['account_list'] ?? [];
                Log::info('企业微信客服账号列表获取成功，共' . count($accountList) . '个客服账号');
                return $accountList;
            } else {
                $errorCode = $result['errcode'] ?? 'unknown';
                $errorMsg = $result['errmsg'] ?? 'unknown error';
                Log::error('获取企业微信客服账号列表失败: 错误码=' . $errorCode . ', 错误信息=' . $errorMsg . ', 完整响应=' . $response);

                // 特殊处理常见错误
                if ($errorCode == 60020) {
                    Log::error('企业微信IP白名单限制: 当前服务器IP不在企业微信应用的可信IP列表中。');
                    Log::error('解决方案: 请在企业微信管理后台 -> 应用管理 -> 选择对应应用 -> 网络配置 中添加服务器IP到白名单');
                } elseif ($errorCode == 45009) {
                    Log::error('企业微信API调用频率限制: 请稍后重试');
                } elseif ($errorCode == 40014) {
                    Log::error('企业微信access_token无效: 尝试重新获取');
                    // 清除缓存中的access_token
                    Cache::delete('wechat_work_access_token');
                } elseif ($errorCode == 48002) {
                    Log::error('企业微信客服功能未开启或API接口无权限: 请在企业微信管理后台开启客服功能');
                    Log::error('解决方案1: 请在企业微信管理后台 -> 应用管理 -> 客服 -> 开启客服功能');
                    Log::error('解决方案2: 请检查自建应用是否有权限调用客服API，可能需要联系企业微信管理员开通权限');
                }

                return false;
            }

        } catch (\Exception $e) {
            Log::error('获取企业微信客服账号列表异常: ' . $e->getMessage() . ', 堆栈: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 自动获取可用的客服账号ID
     *
     * @return string|false 返回客服账号ID或false
     */
    public function getAvailableCustomerServiceAccountId()
    {
        try {
            // 如果已经配置了open_kfid，直接返回
            if (!empty($this->openKfId)) {
                Log::info('使用已配置的企业微信客服账号ID: ' . $this->openKfId);
                return $this->openKfId;
            }

            // 获取客服账号列表
            $accountList = $this->getCustomerServiceAccountList();
            if (!$accountList || empty($accountList)) {
                Log::error('未获取到企业微信客服账号列表');
                return false;
            }

            // 查找状态为正常的客服账号
            foreach ($accountList as $account) {
                $openKfId = $account['open_kfid'] ?? '';
                if (!empty($openKfId)) {
                    // 检查账号状态，如果没有status字段或者status不为1，记录警告但仍使用
                    if (!isset($account['status'])) {
                        Log::warning('企业微信客服账号没有状态字段，默认为可用: ' . $openKfId);
                    } elseif ($account['status'] != 1) {
                        Log::warning('企业微信客服账号状态不为正常(status=' . $account['status'] . ')，但仍尝试使用: ' . $openKfId);
                    } else {
                        Log::info('自动获取到可用的企业微信客服账号ID: ' . $openKfId);
                    }
                    return $openKfId;
                }
            }

            // 如果没有找到有效的账号，记录详细错误
            Log::error('企业微信客服账号列表中没有有效的open_kfid');
            if (!empty($accountList)) {
                Log::error('客服账号列表详情: ' . json_encode($accountList));
            }

            Log::error('企业微信客服账号列表中没有有效的open_kfid');
            return false;

        } catch (\Exception $e) {
            Log::error('自动获取企业微信客服账号ID异常: ' . $e->getMessage() . ', 堆栈: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 构建支付消息内容
     *
     * @param string $tradeNo 订单号
     * @param float $amount 支付金额
     * @param array $orderInfo 订单信息
     * @return string
     */
    private function buildPaymentMessage(string $tradeNo, float $amount, array $orderInfo = []): string
    {
        $productName = $orderInfo['name'] ?? '商品支付';

        return "💰 支付订单信息\n\n" .
               "📦 商品名称：{$productName}\n" .
               "💵 支付金额：¥{$amount}\n" .
               "📋 订单号：{$tradeNo}\n" .
               "⏰ 创建时间：" . date('Y-m-d H:i:s') . "\n\n" .
               "📱 请长按下方二维码完成支付\n" .
               "🔒 支付完成后系统将自动确认";
    }

    /**
     * 处理客服会话访问，自动发送支付消息
     *
     * @param string $sessionId 会话ID
     * @return bool
     */
    public function handleCustomerServiceAccess(string $sessionId): bool
    {
        try {
            $cacheKey = 'wechat_service_' . $sessionId;
            $serviceData = cache($cacheKey);

            if (!$serviceData) {
                Log::warning('客服会话数据不存在: ' . $sessionId);
                return false;
            }

            Log::info('处理客服会话访问: ' . $sessionId . ', 数据: ' . json_encode($serviceData));

            $tempUserId = $serviceData['temp_user_id'];
            $message = $serviceData['message'];
            $mediaId = $serviceData['media_id'];

            // 检查是否已经发送过消息
            if (isset($serviceData['sent']) && $serviceData['sent']) {
                Log::info('客服会话消息已发送过，跳过重复发送: ' . $sessionId);
                return true;
            }

            // 发送文本消息
            Log::info('发送文本消息到用户: ' . $tempUserId);
            $textResult = $this->sendTextMessage($tempUserId, $message);
            Log::info('文本消息发送结果: ' . ($textResult ? '成功' : '失败'));

            // 发送二维码图片
            Log::info('发送图片消息到用户: ' . $tempUserId);
            $imageResult = $this->sendImageMessage($tempUserId, $mediaId);
            Log::info('图片消息发送结果: ' . ($imageResult ? '成功' : '失败'));

            if ($textResult && $imageResult) {
                // 标记为已发送，避免重复发送
                $serviceData['sent'] = true;
                $serviceData['sent_time'] = time();
                cache($cacheKey, $serviceData, 1800);

                Log::info('自动发送支付消息成功: ' . $sessionId);
                return true;
            } else {
                // 记录失败原因
                if (!$textResult) {
                    Log::error('发送文本消息失败: ' . $sessionId);
                }
                if (!$imageResult) {
                    Log::error('发送图片消息失败: ' . $sessionId);
                }
            }

            return false;

        } catch (\Exception $e) {
            Log::error('处理客服会话访问失败: ' . $e->getMessage() . '，堆栈: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 处理客服会话访问，使用真实用户ID发送支付消息
     *
     * @param string $sessionId 会话ID
     * @param string $userId 企业微信用户ID
     * @return bool
     */
    public function handleCustomerServiceAccessWithUser(string $sessionId, string $userId): bool
    {
        try {
            $cacheKey = 'wechat_service_' . $sessionId;
            $serviceData = cache($cacheKey);

            if (!$serviceData) {
                Log::warning('客服会话数据不存在: ' . $sessionId);
                return false;
            }

            Log::info('使用真实用户ID处理客服会话访问: ' . $sessionId . ', 用户ID: ' . $userId . ', 数据: ' . json_encode($serviceData));

            $message = $serviceData['message'];
            $mediaId = $serviceData['media_id'];

            // 检查是否已经发送过消息
            if (isset($serviceData['sent']) && $serviceData['sent']) {
                Log::info('客服会话消息已发送过，跳过重复发送: ' . $sessionId);
                return true;
            }

            // 发送文本消息
            Log::info('发送文本消息到真实用户: ' . $userId);
            $textResult = $this->sendTextMessage($userId, $message);
            Log::info('文本消息发送结果: ' . ($textResult ? '成功' : '失败'));

            // 发送二维码图片
            Log::info('发送图片消息到真实用户: ' . $userId);
            $imageResult = $this->sendImageMessage($userId, $mediaId);
            Log::info('图片消息发送结果: ' . ($imageResult ? '成功' : '失败'));

            if ($textResult && $imageResult) {
                // 标记为已发送，避免重复发送
                $serviceData['sent'] = true;
                $serviceData['sent_time'] = time();
                $serviceData['real_user_id'] = $userId;
                cache($cacheKey, $serviceData, 1800);

                Log::info('使用真实用户ID自动发送支付消息成功: ' . $sessionId . ', 用户ID: ' . $userId);
                return true;
            } else {
                // 记录失败原因
                if (!$textResult) {
                    Log::error('发送文本消息失败: ' . $sessionId . ', 用户ID: ' . $userId);
                }
                if (!$imageResult) {
                    Log::error('发送图片消息失败: ' . $sessionId . ', 用户ID: ' . $userId);
                }
            }

            return false;

        } catch (\Exception $e) {
            Log::error('使用真实用户ID处理客服会话访问失败: ' . $e->getMessage() . '，堆栈: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 生成支付二维码并发送给用户
     *
     * @param string $userId 用户ID
     * @param string $tradeNo 订单号
     * @param float $amount 支付金额
     * @return bool
     */
    public function sendPaymentQRCode(string $userId, string $tradeNo, float $amount): bool
    {
        try {
            // 生成支付二维码
            $qrCodePath = $this->generatePaymentQRCode($tradeNo, $amount);
            if (!$qrCodePath) {
                Log::error('生成支付二维码失败: 订单号=' . $tradeNo);
                return false;
            }

            // 上传二维码到企业微信（带重试机制）
            $mediaId = $this->uploadMediaWithRetry($qrCodePath, 3);
            if (!$mediaId) {
                Log::error('上传二维码到企业微信失败: 订单号=' . $tradeNo);
                // 清理临时文件
                if (file_exists($qrCodePath)) {
                    unlink($qrCodePath);
                }
                return false;
            }

            // 发送二维码图片
            $result = $this->sendImageMessage($userId, $mediaId);

            // 发送支付说明
            if ($result) {
                $paymentText = "💰 支付金额：¥{$amount}\n" .
                              "📋 订单号：{$tradeNo}\n" .
                              "📱 请长按上方二维码进行支付\n" .
                              "⏰ 支付完成后系统将自动确认";

                $this->sendTextMessage($userId, $paymentText);
                Log::info('企业微信支付二维码发送成功: 订单号=' . $tradeNo);
            } else {
                Log::error('发送企业微信图片消息失败: 订单号=' . $tradeNo);
            }

            // 清理临时文件
            if (file_exists($qrCodePath)) {
                unlink($qrCodePath);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('发送支付二维码异常: ' . $e->getMessage() . ', 订单号=' . $tradeNo);
            return false;
        }
    }

    /**
     * 带重试机制的媒体文件上传
     *
     * @param string $filePath 文件路径
     * @param int $maxRetries 最大重试次数
     * @param string $type 媒体文件类型
     * @return string|false 返回media_id或false
     */
    public function uploadMediaWithRetry(string $filePath, int $maxRetries = 3, string $type = 'image')
    {
        $lastError = '';

        for ($i = 0; $i < $maxRetries; $i++) {
            $mediaId = $this->uploadMedia($filePath, $type);

            if ($mediaId) {
                if ($i > 0) {
                    Log::info("企业微信媒体文件上传重试成功: 第{$i}次重试");
                }
                return $mediaId;
            }

            // 如果不是最后一次重试，等待一段时间再重试
            if ($i < $maxRetries - 1) {
                $waitTime = ($i + 1) * 2; // 递增等待时间：2秒、4秒、6秒...
                Log::warning("企业微信媒体文件上传失败，{$waitTime}秒后进行第" . ($i + 1) . "次重试");
                sleep($waitTime);
            }
        }

        Log::error("企业微信媒体文件上传失败，已重试{$maxRetries}次");
        return false;
    }

    /**
     * 通用的消息发送方法（带重试机制）
     *
     * @param string $userId 用户ID
     * @param array $messageData 消息数据
     * @param int $maxRetries 最大重试次数
     * @return bool
     */
    public function sendMessageWithRetry(string $userId, array $messageData, int $maxRetries = 3): bool
    {
        $lastError = '';

        for ($i = 0; $i < $maxRetries; $i++) {
            $result = $this->sendMessage($userId, $messageData);

            if ($result) {
                if ($i > 0) {
                    Log::info("企业微信消息发送重试成功: 第{$i}次重试");
                }
                return true;
            }

            // 如果不是最后一次重试，等待一段时间再重试
            if ($i < $maxRetries - 1) {
                $waitTime = ($i + 1) * 2; // 递增等待时间：2秒、4秒、6秒...
                Log::warning("企业微信消息发送失败，{$waitTime}秒后进行第" . ($i + 1) . "次重试");
                sleep($waitTime);
            }
        }

        Log::error("企业微信消息发送失败，已重试{$maxRetries}次");
        return false;
    }

    /**
     * 通用的消息发送方法
     *
     * @param string $userId 用户ID
     * @param array $messageData 消息数据
     * @return bool
     */
    private function sendMessage(string $userId, array $messageData): bool
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::error('获取企业微信access_token失败');
                return false;
            }

            $url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={$accessToken}";

            // 构建基础消息数据
            $data = [
                'touser' => $userId,
                'agentid' => $this->agentId,
            ];

            // 合并消息类型特定数据
            $data = array_merge($data, $messageData);

            $response = $this->httpPost($url, json_encode($data));
            $result = json_decode($response, true);

            if (isset($result['errcode']) && $result['errcode'] == 0) {
                // 记录消息发送成功
                $this->logMessageStatus($userId, $messageData['msgtype'] ?? 'unknown', true, $result);
                return true;
            } else {
                // 记录消息发送失败
                $this->logMessageStatus($userId, $messageData['msgtype'] ?? 'unknown', false, $result);
                Log::error('发送企业微信消息失败: ' . $response);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('发送企业微信消息异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 记录消息发送状态
     *
     * @param string $userId 用户ID
     * @param string $msgType 消息类型
     * @param bool $success 是否成功
     * @param array $result 结果数据
     */
    private function logMessageStatus(string $userId, string $msgType, bool $success, array $result): void
    {
        try {
            $logData = [
                'user_id' => $userId,
                'msg_type' => $msgType,
                'success' => $success ? 1 : 0,
                'errcode' => $result['errcode'] ?? 0,
                'errmsg' => $result['errmsg'] ?? '',
                'msgid' => $result['msgid'] ?? '',
                'create_time' => time()
            ];

            // 这里可以将日志保存到数据库或文件中
            Log::info('企业微信消息发送状态: ' . json_encode($logData));

            // 如果有数据库表存储消息状态，可以在这里保存
            // $messageLog = new \app\admin\model\WechatWorkMessageLog();
            // $messageLog->save($logData);

        } catch (\Exception $e) {
            Log::error('记录企业微信消息状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成支付二维码
     *
     * @param string $tradeNo 订单号
     * @param float $amount 支付金额
     * @return string|false 返回二维码文件路径或false
     */
    private function generatePaymentQRCode(string $tradeNo, float $amount)
    {
        try {
            // 获取订单信息
            $order = \app\admin\model\SystemOrder::where('trade_no', $tradeNo)->find();
            if (!$order) {
                Log::error('订单不存在: ' . $tradeNo);
                return false;
            }

            // 获取通道信息
            $passage = \app\admin\model\SystemPassage::find($order['pid']);
            if (!$passage) {
                Log::error('支付通道不存在: pid=' . $order['pid'] . ', 订单号=' . $tradeNo);
                return false;
            }

            // 生成支付链接
            $paymentUrl = $this->generatePaymentUrl($order, $passage);
            if (!$paymentUrl) {
                Log::error('生成支付链接失败: 订单号=' . $tradeNo);
                return false;
            }

            // 生成二维码文件路径
            $qrCodePath = runtime_path() . 'temp/qrcode_' . $tradeNo . '_' . time() . '.png';

            // 确保目录存在
            $dir = dirname($qrCodePath);
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    Log::error('创建二维码目录失败: ' . $dir);
                    return false;
                }
            }

            // 检查目录是否可写
            if (!is_writable($dir)) {
                Log::error('二维码目录不可写: ' . $dir);
                return false;
            }

            // 使用QR码库生成二维码
            $qr_code = new \app\common\service\QrcodeService();
            $content = $qr_code->createServer($paymentUrl, 300); // 增大二维码尺寸到300px

            if (empty($content)) {
                Log::error('二维码内容生成失败: 支付链接=' . $paymentUrl);
                return false;
            }

            // 写入文件
            $bytesWritten = file_put_contents($qrCodePath, $content);
            if ($bytesWritten === false) {
                Log::error('二维码文件写入失败: ' . $qrCodePath);
                return false;
            }

            // 验证文件是否成功创建且大小合理
            if (!file_exists($qrCodePath)) {
                Log::error('二维码文件创建失败: ' . $qrCodePath);
                return false;
            }

            $fileSize = filesize($qrCodePath);
            if ($fileSize < 100) { // 二维码文件应该至少有100字节
                Log::error('二维码文件大小异常: ' . $fileSize . ' bytes, 路径=' . $qrCodePath);
                unlink($qrCodePath);
                return false;
            }

            Log::info('二维码生成成功: 订单号=' . $tradeNo . ', 文件大小=' . $fileSize . ' bytes');
            return $qrCodePath;

        } catch (\Exception $e) {
            Log::error('生成支付二维码异常: ' . $e->getMessage() . ', 订单号=' . $tradeNo);
            return false;
        }
    }

    /**
     * 生成支付链接
     *
     * @param object $order 订单信息
     * @param object $passage 通道信息
     * @return string|false
     */
    private function generatePaymentUrl($order, $passage)
    {
        // 根据不同支付方式生成对应的支付链接
        switch ($passage['type']) {
            case 1: // 微信支付
                return $this->generateWechatPayUrl($order, $passage);
            case 2: // 支付宝
                return $this->generateAlipayUrl($order, $passage);
            default:
                // 默认跳转到支付页面
                return request()->domain() . '/pay/page?trade_no=' . $order['trade_no'];
        }
    }

    /**
     * 生成微信支付链接
     */
    private function generateWechatPayUrl($order, $passage)
    {
        // 这里可以集成微信支付API生成支付链接
        return request()->domain() . '/pay/page?trade_no=' . $order['trade_no'];
    }

    /**
     * 生成支付宝支付链接
     */
    private function generateAlipayUrl($order, $passage)
    {
        // 这里可以集成支付宝API生成支付链接
        return request()->domain() . '/pay/page?trade_no=' . $order['trade_no'];
    }

    /**
     * 处理企业微信回调消息
     *
     * @param array $message 消息内容
     * @return array 回复消息
     */
    public function handleMessage(array $message): array
    {
        $msgType = $message['MsgType'] ?? '';
        $content = $message['Content'] ?? '';
        $fromUser = $message['FromUserName'] ?? '';
        $event = $message['Event'] ?? '';
        $token = $message['Token'] ?? '';
        $openKfId = $message['OpenKfId'] ?? '';
        $changeType = $message['ChangeType'] ?? '';
        $userId = $message['UserId'] ?? '';
        $externalUserId = $message['ExternalUserId'] ?? '';

        // 记录所有接收到的消息，便于调试
        Log::info('处理企业微信消息: MsgType=' . $msgType . ', Event=' . $event . ', FromUser=' . $fromUser .
                 ', Token=' . $token . ', OpenKfId=' . $openKfId . ', ChangeType=' . $changeType);
        Log::info('完整消息内容: ' . json_encode($message));

        // 处理企业微信客服事件
        if ($msgType === 'event') {
            // 处理客服会话事件
            if ($event === 'kf_msg_or_event' || $event === 'kf_session') {
                Log::info('处理企业微信客服事件: token=' . $token . ', openKfId=' . $openKfId);
                
                // 尝试从token中提取会话ID
                $sessionId = '';
                if (!empty($token)) {
                    // token格式可能是 ENCxxxxx，我们需要提取其中的state信息
                    // 这里我们假设state信息被编码在token中
                    $sessionId = $this->extractSessionIdFromToken($token);
                }
                
                if (!empty($sessionId)) {
                    // 处理客服会话访问，自动发送支付消息
                    $result = $this->handleCustomerServiceAccess($sessionId);
                    if ($result) {
                        Log::info('企业微信客服事件处理成功，已自动发送支付消息: ' . $sessionId);
                        return ['type' => 'text', 'content' => ''];
                    } else {
                        Log::error('企业微信客服事件处理失败: ' . $sessionId);
                    }
                } else {
                    Log::warning('无法从企业微信客服事件中提取会话ID: token=' . $token);
                    
                    // 如果无法从token中提取会话ID，尝试使用最新的会话
                    $recentSessions = $this->getRecentServiceCacheKeys();
                    if (!empty($recentSessions)) {
                        $sessionId = $recentSessions[0];
                        Log::info('使用最新的客服会话: ' . $sessionId);
                        
                        // 处理客服会话访问，自动发送支付消息
                        $result = $this->handleCustomerServiceAccess($sessionId);
                        if ($result) {
                            Log::info('企业微信客服事件处理成功（使用最新会话）: ' . $sessionId);
                            return ['type' => 'text', 'content' => ''];
                        } else {
                            Log::error('企业微信客服事件处理失败（使用最新会话）: ' . $sessionId);
                        }
                    }
                }
                
                return ['type' => 'text', 'content' => ''];
            }
            
            // 处理其他类型的事件
            Log::info('处理其他类型事件: Event=' . $event . ', ChangeType=' . $changeType);
        }

        // 处理文本消息
        if ($msgType === 'text') {
            return $this->handleTextMessage($fromUser, $content);
        }

        // 如果是客服消息，尝试处理
        if (!empty($openKfId)) {
            Log::info('检测到客服消息: openKfId=' . $openKfId);
            
            // 尝试从token中提取会话ID
            $sessionId = '';
            if (!empty($token)) {
                $sessionId = $this->extractSessionIdFromToken($token);
            }
            
            if (!empty($sessionId)) {
                // 处理客服会话访问，自动发送支付消息
                $result = $this->handleCustomerServiceAccess($sessionId);
                if ($result) {
                    Log::info('客服消息处理成功，已自动发送支付消息: ' . $sessionId);
                    return ['type' => 'text', 'content' => ''];
                } else {
                    Log::error('客服消息处理失败: ' . $sessionId);
                }
            } else {
                Log::warning('无法从客服消息中提取会话ID: token=' . $token);
            }
        }

        return ['type' => 'text', 'content' => '暂不支持此类型消息'];
    }

    /**
     * 处理加密的企业微信回调消息
     *
     * @param string $msgSignature 消息签名
     * @param string $timestamp 时间戳
     * @param string $nonce 随机数
     * @param string $encryptMsg 加密消息
     * @return array 回复消息
     */
    public function handleEncryptedMessage(string $msgSignature, string $timestamp, string $nonce, string $encryptMsg): array
    {
        // 解密消息
        $message = $this->decryptMessage($msgSignature, $timestamp, $nonce, $encryptMsg);

        if (empty($message)) {
            return ['type' => 'text', 'content' => '消息解密失败'];
        }

        // 处理解密后的消息
        return $this->handleMessage($message);
    }

    /**
     * 处理文本消息
     *
     * @param string $userId 用户ID
     * @param string $content 消息内容
     * @return array
     */
    private function handleTextMessage(string $userId, string $content): array
    {
        $content = trim($content);

        // 支付相关关键词
        $paymentKeywords = get_setting('wechat_work_payment_keywords', '支付,付款,买单,结账,充值,购买');
        $keywords = explode(',', $paymentKeywords);

        foreach ($keywords as $keyword) {
            $keyword = trim($keyword);
            if (strpos($content, $keyword) !== false) {
                // 提取金额
                if (preg_match('/(\d+\.?\d*)/', $content, $matches)) {
                    $amount = floatval($matches[1]);
                    if ($amount > 0) {
                        // 查找缓存中的订单信息
                        $cacheKey = 'wechat_order_amount_' . $amount;
                        $orderInfo = cache($cacheKey);

                        if ($orderInfo) {
                            // 发送对应订单的二维码
                            $this->sendPaymentQRCode($userId, $orderInfo['trade_no'], $amount);
                            return ['type' => 'text', 'content' => ''];
                        } else {
                            // 创建新的临时订单
                            $tradeNo = $this->createTempOrder($userId, $amount);
                            if ($tradeNo) {
                                $this->sendPaymentQRCode($userId, $tradeNo, $amount);
                                return ['type' => 'text', 'content' => ''];
                            }
                        }
                    }
                }

                return [
                    'type' => 'text',
                    'content' => "请发送具体金额，例如：支付100 或 充值50.5"
                ];
            }
        }

        // 默认回复
        return [
            'type' => 'text',
            'content' => "您好！欢迎使用企业微信客服支付。\n\n" .
                        "💰 发送：支付+金额（如：支付100）\n" .
                        "📱 系统将自动回复支付二维码\n" .
                        "🔒 长按二维码即可完成支付"
        ];
    }

    /**
     * 创建临时订单
     *
     * @param string $userId 用户ID
     * @param float $amount 金额
     * @return string|false 返回订单号或false
     */
    private function createTempOrder(string $userId, float $amount)
    {
        try {
            $tradeNo = 'WW' . date('YmdHis') . rand(1000, 9999);

            $orderData = [
                'trade_no' => $tradeNo,
                'out_trade_no' => $tradeNo,
                'pid' => 1, // 默认通道
                'type' => 1, // 默认微信支付
                'name' => '企业微信客服支付',
                'money' => $amount,
                'truemoney' => $amount,
                'notify_url' => request()->domain() . '/wechatwork/notify',
                'return_url' => request()->domain() . '/wechatwork/return',
                'status' => 0,
                'create_time' => time(),
                'wechat_work_user' => $userId
            ];

            $order = new \app\admin\model\SystemOrder();
            $result = $order->save($orderData);

            return $result ? $tradeNo : false;

        } catch (\Exception $e) {
            Log::error('创建企业微信临时订单失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * HTTP GET请求
     */
    private function httpGet(string $url): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        // 启用HTTP错误码获取
        curl_setopt($ch, CURLOPT_FAILONERROR, false);

        // 获取详细请求信息
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_VERBOSE, false);

        // 记录请求信息
        $requestInfo = [
            'url' => $url
        ];

        $response = curl_exec($ch);

        // 获取HTTP状态码和CURL错误信息
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);

        // 记录响应信息
        $responseInfo = [
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'curl_errno' => $curlErrno,
            'response_size' => is_string($response) ? strlen($response) : 0
        ];

        Log::info('HTTP GET请求信息: ' . json_encode($requestInfo));
        Log::info('HTTP GET响应信息: ' . json_encode($responseInfo));

        // 如果有CURL错误，记录详细错误信息
        if ($curlErrno) {
            Log::error('HTTP GET CURL错误: ' . $curlError . ' (错误码: ' . $curlErrno . ')');
        }

        // 如果HTTP状态码不是200，记录错误
        if ($httpCode != 200) {
            Log::error('HTTP GET状态码错误: ' . $httpCode . ', 响应: ' . $response);
        }

        curl_close($ch);

        return $response ?: '';
    }

    /**
     * HTTP POST请求
     */
    private function httpPost(string $url, $data, bool $isFile = false): string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        // 启用HTTP错误码获取
        curl_setopt($ch, CURLOPT_FAILONERROR, false);

        // 获取详细请求信息
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_VERBOSE, false);

        // 记录请求信息
        $requestInfo = [
            'url' => $url,
            'data_size' => is_string($data) ? strlen($data) : 'unknown',
            'is_file' => $isFile
        ];

        if (!$isFile) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data)
            ]);
        }

        $response = curl_exec($ch);

        // 获取HTTP状态码和CURL错误信息
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);

        // 记录响应信息
        $responseInfo = [
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'curl_errno' => $curlErrno,
            'response_size' => is_string($response) ? strlen($response) : 0
        ];

        Log::info('HTTP POST请求信息: ' . json_encode($requestInfo));
        Log::info('HTTP POST响应信息: ' . json_encode($responseInfo));

        // 如果有CURL错误，记录详细错误信息
        if ($curlErrno) {
            Log::error('HTTP POST CURL错误: ' . $curlError . ' (错误码: ' . $curlErrno . ')');
        }

        // 如果HTTP状态码不是200，记录错误
        if ($httpCode != 200) {
            Log::error('HTTP POST状态码错误: ' . $httpCode . ', 响应: ' . $response);
        }

        curl_close($ch);

        return $response ?: '';
    }



    /**
     * 创建简单的二维码图片
     *
     * @param string $text 二维码内容
     * @param string $filePath 保存路径
     */
    private function createSimpleQRCode(string $text, string $filePath)
    {
        // 这里应该使用专门的二维码生成库
        // 为了演示，创建一个简单的占位图片
        $image = imagecreate(200, 200);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);

        // 填充背景
        imagefill($image, 0, 0, $white);

        // 添加一些简单的图案表示二维码
        for ($i = 0; $i < 200; $i += 10) {
            for ($j = 0; $j < 200; $j += 10) {
                if (($i + $j) % 20 == 0) {
                    imagefilledrectangle($image, $i, $j, $i + 8, $j + 8, $black);
                }
            }
        }

        // 保存图片
        imagepng($image, $filePath);
        imagedestroy($image);
    }

    /**
     * 分析解密错误的具体原因
     *
     * @param int $errCode 错误码
     * @param string $msgSignature 消息签名
     * @param string $timestamp 时间戳
     * @param string $nonce 随机数
     * @param string $encrypt 加密内容
     */
    private function analyzeDecryptError(int $errCode, string $msgSignature, string $timestamp, string $nonce, string $encrypt): void
    {
        switch ($errCode) {
            case -40001:
                Log::error('签名验证错误，尝试分析原因:');

                // 尝试生成正确的签名进行对比
                $params = [$this->token, $timestamp, $nonce, $encrypt];
                sort($params, SORT_STRING);
                $sortedStr = implode($params);
                $expectedSignature = sha1($sortedStr);

                Log::error('期望签名: ' . $expectedSignature);
                Log::error('实际签名: ' . $msgSignature);
                Log::error('签名参数: token=' . $this->token . ', timestamp=' . $timestamp . ', nonce=' . $nonce . ', encrypt前50字符=' . substr($encrypt, 0, 50));

                // 尝试不同的签名组合
                $params2 = [$this->token, $timestamp, $nonce];
                sort($params2, SORT_STRING);
                $sortedStr2 = implode($params2);
                $signature2 = sha1($sortedStr2);
                Log::error('不含encrypt的签名: ' . $signature2);

                break;

            case -40002:
                Log::error('XML解析失败，检查XML格式');
                break;

            case -40003:
                Log::error('SHA加密生成签名失败');
                break;

            case -40004:
                Log::error('EncodingAESKey非法，当前长度: ' . strlen($this->encodingAESKey));
                break;

            case -40005:
                Log::error('CorpID校验错误，当前CorpId: ' . $this->corpId);
                break;

            case -40006:
                Log::error('AES加密失败');
                break;

            case -40007:
                Log::error('AES解密失败，可能是密钥或数据格式问题');
                break;

            case -40008:
                Log::error('解密后得到的buffer非法');
                break;

            case -40009:
                Log::error('Base64加密失败');
                break;

            case -40010:
                Log::error('Base64解密失败');
                break;

            case -40011:
                Log::error('生成XML失败');
                break;

            default:
                Log::error('未知解密错误，错误码: ' . $errCode);
                break;
        }
    }

    /**
     * 从token中提取会话ID
     *
     * @param string $token 加密的token
     * @return string 会话ID
     */
    private function extractSessionIdFromToken(string $token): string
    {
        try {
            Log::info('尝试从token中提取会话ID: ' . $token);
            
            // 如果token是ENC开头，可能是加密的，我们需要尝试其他方法获取会话ID
            if (strpos($token, 'ENC') === 0) {
                Log::info('检测到加密token，尝试通过其他方式获取会话ID');
                
                // 对于加密token，我们尝试以下方法：
                // 1. 检查是否有直接关联的会话
                // 2. 使用最新的会话
                
                // 尝试从最近的客服缓存中查找匹配的会话
                $recentSessions = $this->getRecentServiceCacheKeys();
                if (!empty($recentSessions)) {
                    Log::info('找到最近的客服会话缓存: ' . json_encode($recentSessions));
                    
                    // 返回最新的会话ID
                    return $recentSessions[0];
                }
                
                // 如果没有找到缓存，尝试从token本身提取可能的会话ID
                // 有时候token中可能包含会话ID的部分信息
                $tokenWithoutPrefix = substr($token, 3); // 去掉ENC前缀
                Log::info('尝试从加密token中提取可能的会话ID: ' . substr($tokenWithoutPrefix, 0, 20));
                
                // 尝试查找包含pay_的缓存键
                $allSessionsKey = 'wechat_service_all_sessions';
                $allSessions = cache($allSessionsKey);
                
                if (!empty($allSessions)) {
                    $sessions = json_decode($allSessions, true);
                    if (!empty($sessions) && is_array($sessions)) {
                        // 按时间倒序排序
                        usort($sessions, function($a, $b) {
                            return ($b['create_time'] ?? 0) - ($a['create_time'] ?? 0);
                        });
                        
                        // 返回最新的会话ID
                        $latestSession = $sessions[0];
                        $sessionId = $latestSession['session_id'] ?? '';
                        Log::info('使用最新的客服会话: ' . $sessionId);
                        return $sessionId;
                    }
                }
                
                return '';
            }
            
            // 尝试直接从token中提取会话ID
            // token格式可能是: pay_tradeNo_timestamp 或其他格式
            if (strpos($token, 'pay_') !== false) {
                // 可能是完整的会话ID
                Log::info('token中包含pay_前缀，可能直接是会话ID');
                return $token;
            }
            
            // 尝试解析token中的state参数（如果token是URL编码的）
            if (strpos($token, 'state=') !== false) {
                parse_str(parse_url($token, PHP_URL_QUERY), $params);
                if (isset($params['state']) && strpos($params['state'], 'pay_') !== false) {
                    Log::info('从token的state参数中提取会话ID: ' . $params['state']);
                    return $params['state'];
                }
            }
            
            Log::warning('无法从token中提取会话ID: ' . $token);
            return '';
            
        } catch (\Exception $e) {
            Log::error('从token中提取会话ID异常: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 处理客服事件
     *
     * @param string $openKfId 客服账号ID
     * @param string $token 会话token
     * @return bool 处理结果
     */
    private function handleCustomerServiceEvent(string $openKfId, string $token): bool
    {
        try {
            Log::info('处理客服事件: openKfId=' . $openKfId . ', token=' . substr($token, 0, 20) . '...');
            
            // 尝试从token中提取会话ID
            $sessionId = $this->extractSessionIdFromToken($token);
            if (empty($sessionId)) {
                Log::warning('无法从token中提取会话ID，尝试查找最近的客服会话');
                
                // 尝试从最近的客服缓存中查找
                $recentSessions = $this->getRecentServiceCacheKeys();
                if (!empty($recentSessions)) {
                    $sessionId = $recentSessions[0];
                    Log::info('使用最近的客服会话: ' . $sessionId);
                } else {
                    Log::error('未找到最近的客服会话');
                    return false;
                }
            }
            
            // 处理客服会话访问，自动发送支付消息
            return $this->handleCustomerServiceAccess($sessionId);
            
        } catch (\Exception $e) {
            Log::error('处理客服事件异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取最近的客服会话缓存键
     *
     * @return array 会话ID列表
     */
    private function getRecentServiceCacheKeys(): array
    {
        try {
            // 这里需要根据实际使用的缓存系统来实现
            // 如果是Redis，可以使用SCAN命令或KEYS命令（不推荐在生产环境使用KEYS）
            // 如果是文件缓存，可以扫描缓存目录
            
            // 简化实现：假设我们有一个记录所有客服会话的缓存
            $allSessionsKey = 'wechat_service_all_sessions';
            $allSessions = cache($allSessionsKey);
            
            if (empty($allSessions)) {
                Log::info('未找到客服会话列表缓存');
                return [];
            }
            
            // 按创建时间排序，返回最新的几个会话
            $sessions = json_decode($allSessions, true);
            if (empty($sessions) || !is_array($sessions)) {
                Log::info('客服会话列表为空或格式错误');
                return [];
            }
            
            // 按时间倒序排序
            usort($sessions, function($a, $b) {
                return ($b['create_time'] ?? 0) - ($a['create_time'] ?? 0);
            });
            
            // 返回最新的5个会话ID
            $recentSessions = array_slice($sessions, 0, 5);
            $sessionIds = array_column($recentSessions, 'session_id');
            
            Log::info('获取到最近的客服会话: ' . json_encode($sessionIds));
            return $sessionIds;
            
        } catch (\Exception $e) {
            Log::error('获取最近客服会话缓存键异常: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 更新客服会话列表缓存
     *
     * @param string $sessionId 会话ID
     * @param array $serviceData 会话数据
     * @return bool
     */
    private function updateServiceSessionList(string $sessionId, array $serviceData): bool
    {
        try {
            $allSessionsKey = 'wechat_service_all_sessions';
            $allSessions = cache($allSessionsKey);
            
            // 解析现有的会话列表
            $sessions = [];
            if (!empty($allSessions)) {
                $sessions = json_decode($allSessions, true);
                if (!is_array($sessions)) {
                    $sessions = [];
                }
            }
            
            // 检查是否已存在该会话
            $exists = false;
            foreach ($sessions as &$session) {
                if ($session['session_id'] === $sessionId) {
                    // 更新现有会话信息
                    $session = [
                        'session_id' => $sessionId,
                        'trade_no' => $serviceData['trade_no'],
                        'amount' => $serviceData['amount'],
                        'create_time' => $serviceData['create_time'],
                        'temp_user_id' => $serviceData['temp_user_id']
                    ];
                    $exists = true;
                    break;
                }
            }
            
            // 如果会话不存在，添加新会话
            if (!$exists) {
                $sessions[] = [
                    'session_id' => $sessionId,
                    'trade_no' => $serviceData['trade_no'],
                    'amount' => $serviceData['amount'],
                    'create_time' => $serviceData['create_time'],
                    'temp_user_id' => $serviceData['temp_user_id']
                ];
            }
            
            // 保存更新后的会话列表
            cache($allSessionsKey, json_encode($sessions), 1800); // 缓存30分钟
            
            Log::info('客服会话列表更新成功: ' . $sessionId . ', 总会话数: ' . count($sessions));
            return true;
            
        } catch (\Exception $e) {
            Log::error('更新客服会话列表缓存异常: ' . $e->getMessage());
            return false;
        }
    }


}
